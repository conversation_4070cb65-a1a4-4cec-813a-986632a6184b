APP_DOMAIN=0.0.0.0
ALLOWED_CLIENT_ORIGINS=["http://localhost:3001"]
# Absolute url for your repo's folder, REPLACE with you own
ROOT_FOLDER_PATH=/Users/<USER>/repositories/nordvik
NODE_ENV=development

### LOGIN
LOGIN_RATE_LIMIT_MAX = 5
LOGIN_RATE_LIMIT_TIME_WINDOW = 60000

### POSTGRES LOCAL after running `docker-compose -f .\docker\docker-compose.yml up pg`
POSTGRES_USERNAME=user
POSTGRES_PASSWORD=password
POSTGRES_DB=testdb
POSTGRES_HOST=127.0.0.1

### VITEC DEV
VITEC_URL=https://hubtest.megler.vitec.net
VITEC_USER=inceptech
VITEC_PASSWORD=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
VITEC_INSTALLATION_ID=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>

FINN_API_KEY=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
FINN_URL=https://cache.api.finn.no/iad

### MONGO DEV
MONGO_CONNECTION=<Ask Marton Vincze for a users connection string or do it in cloud.mongodb.com Security=>DatabaseAccess and create a new user>
MONGO_DB=vitec-data-sync-dev

### NEW MONGO (nordvik-api compatible) - for migration testing
MONGODB_URI=<New MongoDB URI with database name included, e.g., mongodb+srv://user:<EMAIL>/database-name>

MAIL_SILENT_FAIL=true
ENABLE_MOCK_SMS_SEND=true
IDFY_ID=t849847327e4c4277b73adccb6e6d4698
IDFY_SECRET=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>

### EIENDOMSVERDI DEV
EIENDOMSVERDI_USER=incepteamapi;nordvik;<EMAIL>;true
EIENDOMSVERDI_PASSWORD=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>

### SMS sending
TWILO_SID=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
TWILO_TOKEN=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
SMS_SERVICE_NAME=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>

### Google Analytics
GOOGLE_ANALYTICS_PRIVATE_KEY=<find here: https://gitlab.com/nordvik-team/nordvik/-/settings/ci_cd>
GOOGLE_ANALYTICS_VIEWING_ID=178915682
GOOGLE_ANALYTICS_EMAIL=<EMAIL>

### Feature flags
UNLEASH_URL=https://eu.app.unleash-hosted.com/eubb1011/api/
UNLEASH_APP_NAME=nordvik-app
UNLEASH_AUTHORIZATION_HEADER=<find here: https://eu.app.unleash-hosted.com/eubb1011/admin/api>

### You can look here for storebrand settings: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu
STOREBRAND_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMII..................Il8pZDw=\n-----END PRIVATE KEY-----\n"
MAPBOX_ACCESS_TOKEN=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
S3_ACCESS_KEY=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
S3_BUCKET=vitec-data-sync-staging
S3_SECRET=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>

#### ELECTRICITY LEADS
SEND_ELECTRICITY_LEAD=true

### NORGES DEV
NORGES_USERNAME=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
NORGES_PASSWORD=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
NORGES_CLIENT_SECRET=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>
NORGES_ACTIVITY_ID=5816

### FORTUM DEV
FORTUM_PASSWORD=<find here: https://eu-north-1.console.aws.amazon.com/elasticbeanstalk/home?region=eu-north-1#/environment/configuration?applicationName=nordvik-backend&environmentId=e-tnvtukeigu>


### FJORDKRAFT DEV
FJORDKRAFT_CLIENT_ID=*********
FJORDKRAFT_CLIENT_SECRET=dce38d915549e4c0f83fbbea35e334bacd868dc448c117eaf661e0088e57a036
FJORDKRAFT_SALE_URL=http://fk-t-ordermodule-conductor.azurewebsites.net/api/v1/Order/ExternalOrder
FJORDKRAFT_TOKEN_URL=https://auth.fjordkraft.no/connect/token
FJORDKRAFT_SCOPE=OrderModuleConductor
FJORDKRAFT_PRIVATE_PRODUCT_ID=238
FJORDKRAFT_COMPANY_PRODUCT_ID=229
FJORDKRAFT_PRODUCT_HUB_ID=72

# FJORDKRAFT TROENDELKRAFT DEV
TRONDELAGKRAFT_SPOT_PRODUCT_ID=238
TRONDELAGKRAFT_SPOT_PRODUCT_HUB_ID=983
TRONDELAGKRAFT_SPOT_COMPANY_PRODUCT_ID=234

TRONDELAGKRAFT_TOBB_SPOT_PRODUCT_ID=234
TRONDELAGKRAFT_TOBBSPOT_PRODUCT_HUB_ID=983


### Backend-frontend communication
JWT_SECRET=anything-you-would-like-for-local-development

PRETTY_LOGGING=true

SEND_VITEC_LEADS=false

###Unleash Feature flags
UNLEASH_URL=https://eu.app.unleash-hosted.com/eubb1011/api/
UNLEASH_APP_NAME=nordvik-app
UNLEASH_AUTHORIZATION_HEADER=*:development.5da2de570eb366083062c31beb5729316e8fc47fd6f8cb0e1e0035a6