import convict from 'convict';
import * as dotenv from 'dotenv';
import type mongoose from 'mongoose';

dotenv.config();
dotenv.config({ path: `.env.${process.env.NODE_ENV ?? ''}` });

export type VitecConfig = {
  apiURL: string;
  username: string;
  password: string;
  installationID: string;
};

export type MapboxConfig = {
  accessToken: string;
  baseUrl: string;
  userName: string;
  styleID: string;
  zoom: number;
  minRelevance: number;
};

export type IdfyConfig = {
  id: string;
  secret: string;
};

export type StorebrandConfig = {
  clientID: string;
  privateKey: string;
  iss: string;
  sub: string;
  aud: string;
  exchangeURL: string;
  sendLeadURL: string;
};

export type SteddyConfig = {
  token: string;
  sendLeadURL: string;
  checkAvailabilityURL: string;
};

export type KokkelorenConfig = {
  accessToken: string;
  sendLeadURL: string;
  isProduction: boolean;
};

export type TelenorConfig = {
  clientID: string;
  clientSecret: string;
  username: string;
  password: string;
  tokenURL: string;
  offerURL: string;
  sendLeadURL: string;
};

export type AwsConfig = {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  secretsManager: {
    arn: string;
  };
};

export type AwsS3Config = {
  accessKeyID: string;
  bucketName: string;
  secret: string;
};

export type PremarketConfig = {
  limit: number;
};

export type RateLimitConfig = {
  max: number;
  timeWindow: number;
};

export type NorgesConfig = {
  username: string;
  password: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_id: string;
  tokenUrl: string;
  saleUrl: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_secret: string;
  activityId: string;
  customerGroup: string;
  productId: string;
};

// Was Hafslund before buying
export type FortumConfig = {
  username: string;
  password: string;
  tokenUrl: string;
  saleUrl: string;
  packageId: string;
  projectId: string;
};

export type FjordkraftConfig = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_id: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_secret: string;
  saleUrl: string;
  tokenUrl: string;
  scope: string;
  privateProductId: number;
  companyProductId: number;
  productHubId: number;
};

export type TrondelagkraftConfig = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_id: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_secret: string;
  saleUrl: string;
  tokenUrl: string;
  scope: string;
  productId: number;
  companyProductId: number;
  productHubId: number;
};

export type HKraftConfig = {
  apiKey: string;
  googleApiKey: string;
  saleUrl: string;
  productId: string;
};

export type SectorAlarmConfig = {
  leadUrl: string;
  apiSubscriptionKey: string;
};
export type LambdaConfig = {
  pdfGenFunctionName: string;
};

export type HjemNoConfig = {
  clientId: string;
  clientSecret: string;
  tokenUrl: string;
  apiBaseUrl: string;
  systemName: string;
  installationId: string;
};

const convictConfig = convict({
  environmentName: {
    doc: 'Nordvik environment name',
    format: String,
    default: 'dev',
    env: 'ENVIRONMENT_NAME',
  },
  slack: {
    webhookBaseUri: {
      doc: 'Base url for slack notifications',
      format: String,
      default: '',
      env: 'SLACK_WEBHOOK_BASE_URI',
    },
    otpSuccessPostfix: {
      doc: 'Last part of otp success webhook url',
      format: String,
      default: '',
      env: 'SLACK_OTP_SUCCCESS_POSTFIX',
    },
    otpErrorPostfix: {
      doc: 'Last part of otp error webhook url',
      format: String,
      default: '',
      env: 'SLACK_OTP_ERROR_POSTFIX',
    },
    settlementSuccessPostfix: {
      doc: 'Last part of otp success webhook url',
      format: String,
      default: '',
      env: 'SLACK_SETTLEMENT_SUCCESS_POSTFIX',
    },
    settlementErrorPostfix: {
      doc: 'Last part of otp error webhook url',
      format: String,
      default: '',
      env: 'SLACK_SETTLEMENT_ERROR_POSTFIX',
    },
    leadSuccessPostfix: {
      doc: 'Last part of leads success webhook url',
      format: String,
      default: '',
      env: 'SLACK_LEADS_SUCCESS_POSTFIX',
    },
    leadErrorPostfix: {
      doc: 'Last part of leads error webhook url',
      format: String,
      default: '',
      env: 'SLACK_LEADS_ERROR_POSTFIX',
    },
    registrationSuccessPostfix: {
      doc: 'Last part of the registration success webhook url',
      format: String,
      default: '',
      env: 'SLACK_REG_SUCCESS_POSTFIX',
    },
    registrationErrorPostfix: {
      doc: 'Last part of the registration error webhook url',
      format: String,
      default: '',
      env: 'SLACK_REG_ERROR_POSTFIX',
    },
    pepSuccessPostfix: {
      doc: 'Last part of pep success webhook url',
      format: String,
      default: '',
      env: 'SLACK_PEP_SUCCESS_POSTFIX',
    },
    pepErrorPostfix: {
      doc: 'Last part of pep error webhook url',
      format: String,
      default: '',
      env: 'SLACK_PEP_ERROR_POSTFIX',
    },
  },
  backendApiKey: {
    doc: 'Backend Apikey',
    format: String,
    default: 'change_me',
    env: 'BACKEND_API_KEY',
  },
  sendElectricityLead: {
    doc: 'Send electricity lead',
    format: Boolean,
    default: false,
    env: 'SEND_ELECTRICITY_LEAD',
  },
  norges: {
    username: {
      doc: 'Fortum api username',
      format: String,
      default: 'NordvikDES',
      env: 'FORTUM_USERNAME',
    },
    password: {
      doc: 'Fortum api password',
      format: String,
      default: '',
      env: 'FORTUM_PASSWORD',
    },
    tokenUrl: {
      doc: 'Fortum api token url',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: 'https://hs-am-uat.azure-api.net/security/api/Partner/Token',
      env: 'FORTUM_TOKEN_URL',
    },
    saleUrl: {
      doc: 'Fortum api sale url',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: 'https://hs-am-uat.azure-api.net/eaas/api/order/Add',
      env: 'FORTUM_SALE_URL',
    },
    projectId: {
      doc: 'Fortum norges api package id',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: '778157',
      env: 'FORTUM_NORGES_PROJECT_ID',
    },
    packageId: {
      doc: 'Fortum api package id',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: '909182',
      env: 'FORTUM_PACKAGE_ID',
    },
  },
  fortum: {
    username: {
      doc: 'Fortum api username',
      format: String,
      default: 'NordvikDES',
      env: 'FORTUM_USERNAME',
    },
    password: {
      doc: 'Fortum api password',
      format: String,
      default: '',
      env: 'FORTUM_PASSWORD',
    },
    tokenUrl: {
      doc: 'Fortum api token url',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: 'https://hs-am-uat.azure-api.net/security/api/Partner/Token',
      env: 'FORTUM_TOKEN_URL',
    },
    saleUrl: {
      doc: 'Fortum api sale url',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: 'https://hs-am-uat.azure-api.net/eaas/api/order/Add',
      env: 'FORTUM_SALE_URL',
    },
    packageId: {
      doc: 'Fortum api package id',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: '909182',
      env: 'FORTUM_PACKAGE_ID',
    },
    projectId: {
      doc: 'Fortum api package id',
      format: String,
      // the test environment of Fortum is the previous hafslund test API
      default: '635448',
      env: 'FORTUM_PROJECT_ID',
    },
  },
  fjordkraft: {
    client_id: {
      doc: 'Fjordkraft api oauth 2 client ID',
      format: String,
      default: '*********',
      env: 'FJORDKRAFT_CLIENT_ID',
    },
    client_secret: {
      doc: 'Fjordkraft api oauth 2 client secret',
      format: String,
      default: 'dce38d915549e4c0f83fbbea35e334bacd868dc448c117eaf661e0088e57a036',
      env: 'FJORDKRAFT_CLIENT_SECRET',
    },
    saleUrl: {
      doc: 'Fjordkraft api sale url',
      format: String,
      default: 'http://fk-t-ordermodule-conductor.azurewebsites.net/api/v1/Order/ExternalOrder',
      env: 'FJORDKRAFT_SALE_URL',
    },
    tokenUrl: {
      doc: 'Fjordkraft api oauth 2 token url',
      format: String,
      default: 'https://auth.fjordkraft.no/connect/token',
      env: 'FJORDKRAFT_TOKEN_URL',
    },
    scope: {
      doc: 'Fjordkraft api oauth 2 scope',
      format: String,
      default: 'OrderModuleConductor',
      env: 'FJORDKRAFT_SCOPE',
    },
    privateProductId: {
      doc: 'Fjordkraft api product ID for private customers',
      format: Number,
      default: 238,
      env: 'FJORDKRAFT_PRIVATE_PRODUCT_ID',
    },
    companyProductId: {
      doc: 'Fjordkraft api product ID for buisness customers',
      format: Number,
      default: 229,
      env: 'FJORDKRAFT_COMPANY_PRODUCT_ID',
    },
    productHubId: {
      doc: 'Fjordkraft api product hub ID',
      format: Number,
      default: 72,
      env: 'FJORDKRAFT_PRODUCT_HUB_ID',
    },
  },
  trondelagSpot: {
    client_id: {
      doc: 'Fjordkraft api oauth 2 client ID',
      format: String,
      default: '*********',
      env: 'FJORDKRAFT_CLIENT_ID',
    },
    client_secret: {
      doc: 'Fjordkraft api oauth 2 client secret',
      format: String,
      default: 'dce38d915549e4c0f83fbbea35e334bacd868dc448c117eaf661e0088e57a036',
      env: 'FJORDKRAFT_CLIENT_SECRET',
    },
    saleUrl: {
      doc: 'Fjordkraft api sale url',
      format: String,
      default: 'http://fk-t-ordermodule-conductor.azurewebsites.net/api/v1/Order/ExternalOrder',
      env: 'FJORDKRAFT_SALE_URL',
    },
    tokenUrl: {
      doc: 'Fjordkraft api oauth 2 token url',
      format: String,
      default: 'https://auth.fjordkraft.no/connect/token',
      env: 'FJORDKRAFT_TOKEN_URL',
    },
    scope: {
      doc: 'Fjordkraft api oauth 2 scope',
      format: String,
      default: 'OrderModuleConductor',
      env: 'FJORDKRAFT_SCOPE',
    },
    productId: {
      doc: 'Fjordkraft api product ID for private customers',
      format: Number,
      default: 253,
      env: 'TRONDELAGKRAFT_SPOT_PRODUCT_ID',
    },
    companyProductId: {
      doc: 'Fjordkraft api product ID for buisness customers',
      format: Number,
      default: 2,
      env: 'TRONDELAGKRAFT_SPOT_COMPANY_PRODUCT_ID',
    },
    productHubId: {
      doc: 'Fjordkraft api product hub ID',
      format: Number,
      default: 983,
      env: 'TRONDELAGKRAFT_SPOT_PRODUCT_HUB_ID',
    },
  },
  trondelagTobbSpot: {
    client_id: {
      doc: 'Fjordkraft api oauth 2 client ID',
      format: String,
      default: '*********',
      env: 'FJORDKRAFT_CLIENT_ID',
    },
    client_secret: {
      doc: 'Fjordkraft api oauth 2 client secret',
      format: String,
      default: 'dce38d915549e4c0f83fbbea35e334bacd868dc448c117eaf661e0088e57a036',
      env: 'FJORDKRAFT_CLIENT_SECRET',
    },
    saleUrl: {
      doc: 'Fjordkraft api sale url',
      format: String,
      default: 'http://fk-t-ordermodule-conductor.azurewebsites.net/api/v1/Order/ExternalOrder',
      env: 'FJORDKRAFT_SALE_URL',
    },
    tokenUrl: {
      doc: 'Fjordkraft api oauth 2 token url',
      format: String,
      default: 'https://auth.fjordkraft.no/connect/token',
      env: 'FJORDKRAFT_TOKEN_URL',
    },
    scope: {
      doc: 'Fjordkraft api oauth 2 scope',
      format: String,
      default: 'OrderModuleConductor',
      env: 'FJORDKRAFT_SCOPE',
    },
    productId: {
      doc: 'Fjordkraft api product ID for private customers',
      format: Number,
      default: 234,
      env: 'TRONDELAGKRAFT_TOBB_SPOT_PRODUCT_ID',
    },
    companyProductId: {
      doc: 'Fjordkraft api product ID for buisness customers',
      format: Number,
      default: 2,
      env: 'TRONDELAGKRAFT_TOBB_SPOT_COMPANY_PRODUCT_ID',
    },
    productHubId: {
      doc: 'Fjordkraft api product hub ID',
      format: Number,
      default: 983,
      env: 'TRONDELAGKRAFT_TOBB_SPOT_PRODUCT_HUB_ID',
    },
  },
  hkraft: {
    apiKey: {
      doc: 'HKraft API key for authentication',
      format: String,
      default: '8d53168e-8fad-4330-a2ef-6d7875825c27',
      env: 'HKRAFT_API_KEY',
    },
    googleApiKey: {
      doc: 'HKraft Google API key for endpoint access',
      format: String,
      default: 'AIzaSyBH2xrLprebOEWpnx3mtZ4dEy4S0xTw_r0',
      env: 'HKRAFT_GOOGLE_API_KEY',
    },
    saleUrl: {
      doc: 'HKraft API endpoint for power orders',
      format: String,
      default: 'https://myhome.test.hkraft.run/v1/powerorder',
      env: 'HKRAFT_SALE_URL',
    },
    productId: {
      doc: 'HKraft product ID for electricity orders',
      format: String,
      default: '0dde93d010674d1ca75e13273d8c8c04',
      env: 'HKRAFT_PRODUCT_ID',
    },
  },
  sectorAlarm: {
    leadUrl: {
      doc: 'Sector Alarm lead url to send API calls to',
      format: String,
      default: 'https://api.testcloud.sectoralarm.net/leads/v2/lead',
      env: 'SECTOR_ALARM_LEAD_URL',
    },
    apiSubscriptionKey: {
      doc: 'Sector Alarm subscription key for API calls',
      format: String,
      default: '95f08f95a18d4708881a5f9d4ffc88a5',
      env: 'SECTOR_ALARM_SUBSCRIPTION_KEY',
    },
  },
  adultYear: {
    doc: 'Adult year.',
    format: Number,
    default: 18,
    env: 'ADULT_YEAR',
  },
  domain: {
    doc: 'Domain name.',
    format: String,
    default: 'http://localhost:3000',
    env: 'APP_DOMAIN',
  },
  frontendDomain: {
    doc: 'Frontend domain name.',
    format: String,
    default: 'http://localhost:3001',
    env: 'FRONTEND_APP_DOMAIN',
  },
  domainSwagger: {
    doc: 'Domain name for swagger.',
    format: String,
    default: 'localhost:3000',
    env: 'APP_DOMAIN_SWAGGER',
  },
  port: {
    doc: 'The port to bind.',
    format: Number,
    default: 3000,
    env: 'PORT',
  },
  logger: {
    level: {
      doc: 'Defines the level of the logger.',
      format: String,
      default: 'debug',
      env: 'LOGGER_LEVEL',
    },
    pretty_logging: {
      doc: 'Defines if pretty logging is enabled.',
      format: Boolean,
      default: false,
      env: 'PRETTY_LOGGING',
    },
  },
  saltRounds: {
    doc: 'Salt rounds in number.',
    format: Number,
    default: 10,
    env: 'SALT_ROUNDS',
  },
  twilo: {
    accountSid: {
      doc: 'Twilo account SID.',
      format: String,
      default: 'nordvik',
      env: 'TWILO_SID',
    },
    authToken: {
      doc: 'Twilo secret token.',
      format: String,
      default: 'token',
      env: 'TWILO_TOKEN',
    },
    serviceName: {
      doc: 'SMS Service name (from account).',
      format: String,
      default: 'sms',
      env: 'SMS_SERVICE_NAME',
    },
    disabled: {
      doc: 'Local developement option.',
      format: Boolean,
      default: false,
      env: 'ENABLE_MOCK_SMS_SEND',
    },
    fromNumber: {
      doc: 'Twilio number to send SMS from',
      format: String,
      default: 'MGbed3eef3e4899103c8d7d3c94d9915a9',
      env: 'SMS_FROM_NUMBER',
    },
    disableTextMessages: {
      doc: 'Send twilio sms',
      format: Boolean,
      default: true,
      env: 'DISABLE_TEXT_MESSAGES',
    },
  },
  jwt: {
    secret: {
      doc: 'jwt secret key',
      format: (value) => {
        if (process.env.NODE_ENV === 'production' && value === 'secret') {
          throw new Error('Do not use the default JWT secret in production.');
        }
      },
      default: 'secret',
      env: 'JWT_SECRET',
    },
    expiresIn: {
      tokenService: {
        doc: 'Token expiration time.',
        format: String,
        default: '24h',
        env: 'JWT_EXPIRATION_TIME',
      },
      resetPasswordTokenService: {
        doc: 'Reset password token expiration time.',
        format: String,
        default: '1h',
        env: 'JWT_EXPIRATION_TIME_RESET_PASSWORD',
      },
      emailVerificationTokenService: {
        doc: 'Email verification token expiration time.',
        format: String,
        default: '1h',
        env: 'JWT_EXPIRATION_TIME_EMAIL_VERIFICATION',
      },
    },
    issuer: {
      doc: 'JWT token issuer.',
      format: String,
      default: 'nordvik',
      env: 'JWT_ISSUER',
    },
  },
  pg: {
    logging: {
      doc: 'Pg logging based on this boolean',
      format: Boolean,
      default: false,
      env: 'PG_LOGGER',
    },
  },
  idfy: {
    id: {
      doc: 'Idfy client id',
      format: (value) => {
        if (process.env.NODE_ENV === 'production' && !value) {
          throw new Error('You must set up the `IDFY_ID` env for bank id authentication.');
        }
      },
      default: 'Set a default in .env.development if you need this for testing purposes.',
      env: 'IDFY_ID',
    },
    secret: {
      doc: 'Idfy client secret.',
      format: (value) => {
        if (process.env.NODE_ENV === 'production' && !value) {
          throw new Error('You must set up the `IDFY_SECRET` env for bank id authentication.');
        }
      },
      default: 'Set a default in .env.development if you need this for testing purposes.',
      env: 'IDFY_SECRET',
    },
  },
  mongo: {
    connection: {
      doc: 'Connection string for mongodb.',
      format: String,
      default: 'mongodb://127.0.0.1:27017',
      env: 'MONGO_CONNECTION',
    },
    db: {
      doc: 'Connection db for mongodb',
      format: String,
      default: 'test',
      env: 'MONGO_DB',
    },
  },
  // New MongoDB connection configuration (nordvik-api compatible)
  mongoNew: {
    uri: {
      doc: 'MongoDB URI for new database (nordvik-api format)',
      format: String,
      default: 'mongodb://127.0.0.1:27017/test',
      env: 'MONGODB_URI',
    },
  },
  vitec: {
    apiURL: {
      doc: 'URL for vitec API.',
      format: String,
      default: 'https://vitec.com',
      env: 'VITEC_URL',
    },
    username: {
      doc: 'Username for vitec API.',
      format: String,
      default: 'username',
      env: 'VITEC_USER',
    },
    password: {
      doc: 'Password for vitec API.',
      format: String,
      default: 'password',
      env: 'VITEC_PASSWORD',
    },
    installationID: {
      doc: 'Installation identifier for vitec API.',
      format: String,
      default: 'installationid',
      env: 'VITEC_INSTALLATION_ID',
    },
  },
  cors: {
    allowedOrigins: {
      doc: 'The list of client origins in JSON format. Used for setting the Access-Control-Allow-Origin header.',
      format: String,
      default: '["http://localhost:3001"]',
      env: 'ALLOWED_CLIENT_ORIGINS',
    },
  },
  otp: {
    documentRenderer: {
      doc: 'Instance to render template to PDF',
      format: String,
      default: 'soffice',
      env: 'OTP_DOC_RENDER',
    },
  },
  statistics: {
    finnNo: {
      url: {
        doc: 'URL of finn.no ad statistics url',
        format: String,
        default: 'https://cache.api.finn.no/iad',
        env: 'FINN_URL',
      },
      apiKey: {
        doc: 'Api key for finn url',
        format: String,
        default: '',
        env: 'FINN_API_KEY',
      },
      adUrl: {
        doc: 'URL of finn.no ad',
        format: String,
        default: 'https://www.finn.no',
        env: 'FINN_AD_URL',
      },
    },
    nordvikboligNo: {
      numberOfDays: {
        doc:
          'Determines the number of days for which the page views should be aggregated (for the chart on the estate overview page)',
        format: Number,
        default: 14,
        env: 'STATISTICS_NORDVIKBOLIGNO_NUMBER_OF_DAYS',
      },
      googleAnalytics: {
        viewId: {
          doc: "Viewing ID for nordvikbolig.no's Google Analytics",
          format: String,
          default: '',
          env: 'GOOGLE_ANALYTICS_VIEWING_ID', // TODO: Rename to something which involves nordvikbolig.no for clarity
        },
        email: {
          doc: 'Email address for accessing the Google Analytics dashboard',
          format: String,
          default: '',
          env: 'GOOGLE_ANALYTICS_EMAIL', // TODO: Rename to something which involves nordvikbolig.no for clarity
        },
        privateKey: {
          doc: 'Private key for google analytics',
          format: String,
          default: '',
          env: 'GOOGLE_ANALYTICS_PRIVATE_KEY', // TODO: Rename to something which involves nordvikbolig.no for clarity
        },
      },
    },
    ekstra: {
      apiKey: {
        doc: 'API key for the ekstra statistics api',
        format: String,
        default: '',
        env: 'EKSTRA_STATISTICS_API_KEY',
      },
    },
  },
  mockEndpoints: {
    doc: 'Provides a list of paths to mock',
    format: String,
    default: '[]',
    env: 'MOCK_ENDPOINTS',
  },
  pdfLocation: {
    doc: 'Provides a list of paths to mock',
    format: String,
    default: '/app/documents/Nordvik_losore_tilbehor.pdf',
    env: 'PDF_LOCATION',
  },
  mail: {
    sendgridApikey: {
      doc: 'Sendgrid apikey for email service',
      format: String,
      default: 'apikey',
      env: 'SENDGRID_API_KEY',
    },
    senderAddress: {
      doc: 'Default email address of the sender',
      format: String,
      default: '<EMAIL>',
      env: 'SENDER_MAIL_ADDRESS',
    },
    serviceOffer: {
      hmh: {
        doc: 'Receiver address for the HMH service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'HMH_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      osloTransport: {
        doc: 'Receiver address for the Oslo Transport offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'OSLO_TRANSPORT_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      if: {
        doc: 'Receiver address for the IF service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'IF_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      ssg: {
        doc: 'Receiver address for the SSG service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'SSG_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      trygg: {
        doc: 'Receiver address for the Trygg service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'TRYGG_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      chill: {
        doc: 'Receiver address for the Chill service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'CHILL_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      verketInterior: {
        doc: 'Receiver address for the Verket Interior service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'VERKET_INTERIOR_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
      rental: {
        doc: 'Receiver address for the Rental service offer requests',
        format: String,
        default: '<EMAIL>',
        env: 'RENTAL_SERVICE_OFFER_RECEIVER_ADDRESS',
      },
    },
    silentFail: {
      doc: 'Determines if email sending errors should be reported',
      format: Boolean,
      default: false,
      env: 'MAIL_SILENT_FAIL',
    },
  },
  rootFolder: {
    doc: 'Path of root folder path',
    format: String,
    default: '/app',
    env: 'ROOT_FOLDER_PATH',
  },
  eiendomsverdi: {
    publicInformationServiceUrl: {
      doc: "An absolute URL for Eiendomsverdi's PublicInformationRealtime SOAP service",
      format: String,
      default: 'https://test-api.eiendomsverdi.no/PublicInformationRealtime.svc',
      env: 'EIENDOMSVERDI_PUBLIC_INFORMATION_SERVICE_URL',
    },
    serviceUrl: {
      doc: "An absolute URL for Eiendomsverdi's Service SOAP service",
      format: String,
      default: 'https://test-api.eiendomsverdi.no/Service.svc',
      env: 'EIENDOMSVERDI_SERVICE_URL',
    },
    user: {
      doc: 'Username for Eiendomsverdi authentication',
      format: String,
      default: '',
      env: 'EIENDOMSVERDI_USER',
    },
    password: {
      doc: 'Password for Eiendomsverdi authentication',
      format: String,
      default: '',
      env: 'EIENDOMSVERDI_PASSWORD',
    },
    tokenUrlRest: {
      doc: 'URL to get the JWT auth token with clientId and clientSecret for REST API',
      format: String,
      default: 'https://externaltestapi.eiendomsverdi.no/oauth/token',
      env: 'EIENDOMSVERDI_TOKEN_URL_REST',
    },
    clientIdRest: {
      doc: 'Client ID for the REST API',
      format: String,
      default: '',
      env: 'EIENDOMSVERDI_CLIENT_ID_REST',
    },
    clientSecretRest: {
      doc: 'Client secret for the REST API',
      format: String,
      default: '',
      env: 'EIENDOMSVERDI_CLIENT_SECRET_REST',
    },
    apiUrlRest: {
      doc: 'API url for the REST API',
      format: String,
      default: 'https://externaltestapi.eiendomsverdi.no/realproperty/v1/',
      env: 'EIENDOMSVERDI_API_URL_REST',
    },
    estimateApiUrlRest: {
      doc: 'API url for the REST Estimate API',
      format: String,
      default: 'https://externaltestapi.eiendomsverdi.no/estimate/v1/',
      env: 'EIENDOMSVERDI_ESTIMATE_API_URL_REST',
    },
    cacheTimeoutHours: {
      doc: 'Timeout for invalidating the cache in hours',
      format: Number,
      default: 24,
      env: 'EIENDOMSVERDI_CACHE_TIMEOUT_HOURS',
    },
    cacheFallbackTimeoutHours: {
      doc: 'Timeout for handling API downtime by loading originally timed out cache in hours',
      format: Number,
      default: 24 * 30,
      env: 'EIENDOMSVERDI_CACHE_FALLBACK_TIMEOUT_HOURS',
    },
    cacheableTypes: {
      doc: 'List of Eiendomsverdi types that can be cached',
      format: String,
      default:
        '["GetHousingCooperativeShareSaleHistory","GetEstateSaleHistory","GetMarketEstimate","GetHousingCooperativeMarketEstimate"]',
      env: 'EIENDOMSVERDI_CACHEABLE_TYPES',
    },
  },
  unleash: {
    url: {
      doc: 'The auto-generated api url.',
      format: String,
      default: 'https://eu.app.unleash-hosted.com/eubb1011/api/',
      env: 'UNLEASH_URL',
    },
    instanceId: {
      doc: 'The instanceId for the unleash api. This is only required for self-hosted instances.',
      format: String,
      default: undefined,
      env: 'UNLEASH_INSTANCE_ID',
    },
    appName: {
      doc: 'Unleash app name.',
      format: String,
      default: 'nordvik-app',
      env: 'UNLEASH_APP_NAME',
    },
    authorizationHeader: {
      doc: 'The unleash authorization header. This is only required for managed instances.',
      format: String,
      default: '',
      env: 'UNLEASH_AUTHORIZATION_HEADER',
    },
  },
  awsS3ImgBaseUrl: {
    doc: 'Aws s3 images location base url',
    format: String,
    default: '',
    env: 'AWS_S3_IMG_BASE_URL',
  },
  awsS3DocAndMeterConfig: {
    bucket: {
      doc: 'AWS S3 bucket for otp/settlement documents and meter images',
      format: String,
      default: '',
      env: 'AWS_S3_DOC_METER_BUCKET',
    },
    docFolder: {
      doc: 'Folder for documents',
      format: String,
      default: 'documents',
      env: 'AWS_S3_DOC_FOLDER',
    },
    participantFolder: {
      doc: 'Folder for participand images',
      format: String,
      default: 'participant-images',
      env: 'AWS_S3_PARTICIPANT_FOLDER',
    },
    meterFolder: {
      doc: 'Folder for meter images',
      format: String,
      default: 'meter-images',
      env: 'AWS_S3_IMG_FOLDER',
    },
  },

  telenor: {
    clientID: {
      doc: 'Telenor basic auth username',
      format: String,
      default: '',
      env: 'TELENOR_CLIENT_ID',
    },
    clientSecret: {
      doc: 'Telenor basic auth password',
      format: String,
      default: '',
      env: 'TELENOR_CLIENT_SECRET',
    },
    username: {
      doc: 'Telenor api username',
      format: String,
      default: '',
      env: 'TELENOR_USERNAME',
    },
    password: {
      doc: 'Telenor api password',
      format: String,
      default: '',
      env: 'TELENOR_PASSWORD',
    },
    tokenURL: {
      doc: 'Telenor api token url',
      format: String,
      default: 'https://api.telenor.no/oauth/v2/token',
      env: 'TELENOR_TOKEN_URL',
    },
    offerURL: {
      doc: 'Telenor api offer url',
      format: String,
      default: 'https://api.telenor.no/broadband-search/v2/address/offers',
      env: 'TELENOR_OFFER_URL',
    },
    sendLeadURL: {
      doc: 'Telenor api send lead url',
      format: String,
      default: 'https://api.telenor.no/broadband-order/v1/order',
      env: 'TELENOR_SEND_LEAD_URL',
    },
  },
  storebrand: {
    clientID: {
      doc: 'Client ID for storebrand.',
      format: String,
      default: 'Nordvik',
      env: 'STOREBRAND_CLIENT_ID',
    },
    privateKey: {
      doc: 'Private key for signing token.',
      format: String,
      default: '',
      env: 'STOREBRAND_PRIVATE_KEY',
    },
    iss: {
      doc: 'Iss for token',
      format: String,
      default: 'test',
      env: 'STOREBRAND_TOKEN_ISS',
    },
    aud: {
      doc: 'Aud for token',
      format: String,
      default: 'https://id-t.storebrand.no/auth/realms/partners',
      env: 'STOREBRAND_TOKEN_AUD',
    },
    sub: {
      doc: 'Sub for token',
      format: String,
      default: 'external.nordvik',
      env: 'STOREBRAND_TOKEN_SUB',
    },
    exchangeURL: {
      doc: 'Url for exchanging token',
      format: String,
      default: 'https://id-t.storebrand.no/auth/realms/partners/protocol/openid-connect/token',
      env: 'STOREBRAND_TOKEN_EXCHANGE_URL',
    },
    sendLeadURL: {
      doc: 'Url for send lead',
      format: String,
      default: 'https://nordvik-t.partners.storebrand.no/partner/nordvik/bank/lead/store-all',
      env: 'STOREBRAND_SEND_LEAD_URL',
    },
  },
  steddy: {
    token: { default: 'STEDDY_STATIC_TOKEN', doc: 'Steddy static token', format: String, env: 'STEDDY_TOKEN' },
    checkAvailabilityURL: {
      default: 'https://tilbud.stage.mestergruppen.cloud/api/nordvik/available',
      doc: 'Steddy check availability url',
      format: String,
      env: 'STEDDY_CHECK_AVAILABILITY_URL',
    },
    sendLeadURL: {
      default: 'https://tilbud.stage.mestergruppen.cloud/api/nordvik/new-lead',
      doc: 'Steddy send lead url',
      format: String,
      env: 'STEDDY_SEND_LEAD_URL',
    },
  },
  kokkeloren: {
    accessToken: {
      doc: 'Access token for kokkeloren API',
      env: 'KOKKELOREN_ACCESS_TOKEN',
      format: String,
      default: '',
    },
    sendLeadURL: {
      doc: 'Kokkeloren send lead url',
      format: String,
      default: 'https://api.hubapi.com/crm/v3/objects/contacts',
      env: 'KOKKELOREN_SEND_LEAD_URL',
    },
    isProduction: {
      doc: 'Is production environment',
      format: Boolean,
      default: false,
      env: 'KOKKELOREN_IS_PRODUCTION',
    },
  },
  aws: {
    region: {
      doc: 'Region where the application is hosted on AWS',
      format: String,
      default: '',
      env: 'AWS_REGION',
    },
    accessKeyId: {
      doc: 'Access key ID for AWS',
      format: String,
      default: '',
      env: 'AWS_ACCESS_KEY_ID',
    },
    secretAccessKey: {
      doc: 'Access key secret for AWS',
      format: String,
      default: '',
      env: 'AWS_SECRET_ACCESS_KEY',
    },
    secretsManager: {
      arn: {
        doc: 'ARN of the secret that we use in the application',
        format: String,
        default: '',
        env: 'AWS_SECRET_ARN',
      },
    },
  },
  oldNordikApiUrl: {
    doc: 'url of goodmorning api url',
    format: String,
    default: 'https://www.nordvikbolig.no/api/tips',
    env: 'OLD_NORDVIK_API',
  },
  nordvikboligApiUrl: {
    doc: 'url of nordvikbolig api url',
    format: String,
    default: 'https://api.nordvikbolig.no',
    env: 'NORDVIK_API',
  },
  nordvikboligSyncUrl: {
    doc: 'Nordvik Bolig sync url',
    format: String,
    default: 'https://sync.nordvikbolig.no',
    env: 'NORDVIK_BOLIG_SYNC_URL',
  },
  nordvikboligSyncApiKey: {
    doc: 'Nordvik Bolig API key',
    format: String,
    default: '',
    env: 'NORDVIK_BOLIG_SYNC_API_KEY',
  },
  mapbox: {
    accessToken: {
      doc: 'Access token for mapbox API',
      format: String,
      default: '',
      env: 'MAPBOX_ACCESS_TOKEN',
    },
    baseUrl: {
      doc: 'Base URL for mapbox API',
      format: String,
      default: 'https://api.mapbox.com',
      env: 'MAPBOX_BASE_URL',
    },
    userName: {
      doc: 'Username for mapbox API',
      format: String,
      default: 'nordvik-test',
      env: 'MAPBOX_USER_NAME',
    },
    styleID: {
      doc: 'Style ID for mapbox API',
      format: String,
      default: 'ckg0rv03t10rq19lmt0nuhzhg',
      env: 'MAPBOX_STYLE',
    },
    zoom: {
      doc: 'Zoom value for mapbox API',
      format: Number,
      default: 15,
      env: 'MAPBOX_ZOOM',
    },
    minRelevance: {
      doc: 'Min relevance of geolocation result to return image',
      format: Number,
      default: 0.4,
      env: 'MAPBOX_MIN_RELEVANCE',
    },
  },
  s3Config: {
    accessKeyID: {
      doc: 'Access key id for S3',
      format: String,
      default: '',
      env: 'S3_ACCESS_KEY',
    },
    bucketName: {
      doc: 'Bucket name for S3',
      format: String,
      default: '',
      env: 'S3_BUCKET',
    },
    secret: {
      doc: 'Secret name for S3',
      format: String,
      default: '',
      env: 'S3_SECRET',
    },
  },
  sendVitecLeads: {
    doc: 'send the leads to legacy system',
    format: Boolean,
    default: false,
    env: 'SEND_VITEC_LEADS',
  },
  premarket: {
    limit: {
      doc: 'Limits the number of estates returned by the premarket search',
      format: Number,
      default: 50,
      env: 'PREMARKET_LIMIT',
    },
  },
  hmhBoxRequestEmail: {
    doc: 'url to send hmh box request to',
    format: String,
    default: '<EMAIL>',
    env: 'HMH_EMAIL',
  },
  byggstartEmail: {
    doc: 'url to send byggstart leads to',
    format: String,
    default: '<EMAIL>',
    env: 'BYGGSTART_EMAIL',
  },
  exponovaEmail: {
    doc: 'url to send exponova leads to',
    format: String,
    default: '<EMAIL>',
    env: 'EXPONOVA_EMAIL',
  },
  services: {
    rateLimit: {
      max: {
        doc: 'The number of leads that can be requested in the given time window',
        format: Number,
        default: 3,
        env: 'SERVICES_RATE_LIMIT_MAX',
      },
      timeWindow: {
        doc: 'The time window for lead generation rate limit',
        format: Number,
        default: 10 * 60 * 1000, // 10 minutes in milliseconds
        env: 'SERVICES_RATE_LIMIT_TIME_WINDOW',
      },
    },
  },
  login: {
    rateLimit: {
      max: {
        doc: 'The number of login attempts with an email that can be requested in the given time window',
        format: Number,
        default: 100,
        env: 'LOGIN_RATE_LIMIT_MAX',
      },
      timeWindow: {
        doc: 'The time window for login rate limit',
        format: Number,
        default: 60 * 60 * 1000, // 1 hour in milliseconds
        env: 'LOGIN_RATE_LIMIT_TIME_WINDOW',
      },
    },
  },
  admin: {
    rateLimit: {
      max: {
        doc: 'The number of admin requests that can be made in the given time window',
        format: Number,
        default: 10,
        env: 'ADMIN_RATE_LIMIT_MAX',
      },
      timeWindow: {
        doc: 'The time window for admin rate limit',
        format: Number,
        default: 60 * 1000, // 1 minute in milliseconds
        env: 'ADMIN_RATE_LIMIT_TIME_WINDOW',
      },
    },
  },
  vipps: {
    clientID: {
      doc: 'The client id for VIPPS',
      format: String,
      default: '471b61ec-e50a-466a-961f-a96ded2b63a0',
      env: 'VIPPS_CLIENT_ID',
    },
    clientSecret: {
      doc: 'The client sercet for VIPPS',
      format: String,
      default: '',
      env: 'VIPPS_CLIENT_SECRET',
    },
    redirectUri: {
      doc: 'The redirect URI for VIPPS',
      format: String,
      default: 'http://localhost:3001/customer/signup/vipps/register',
      env: 'VIPPS_REDIRECT_URI',
    },
    getTokenURL: {
      doc: 'Endpoint for getting access token',
      format: String,
      default: 'https://apitest.vipps.no/access-management-1.0/access/oauth2/token',
      env: 'VIPPS_GET_TOKEN_URL',
    },
    getUserInfoURL: {
      doc: 'Endpoint for getting user info',
      format: String,
      default: 'https://apitest.vipps.no/vipps-userinfo-api/userinfo',
      env: 'VIPPS_GET_INFO_URL',
    },
  },
  lipscore: {
    brokerRating: {
      whitelistedIds: {
        doc: 'The id array of the whitelisted 5 star ratings.',
        format: String,
        default: '',
        env: 'LIPSCORE_BROKER_RATING_WHITELISTED_IDS',
      },
    },
  },
  priceGuessingEstate: {
    maxAttempts: {
      doc: 'The maximum amount of guesses a user is allowed to make for the daily price guessing estate.',
      format: Number,
      default: 5,
      env: 'PRICE_GUESSING_ESTATE_MAX_ATTEMPTS',
    },
    hourOfDailyQuiz: {
      doc: 'The hour when the daily guessing game is broadcasted with new estate selection.',
      format: Number,
      default: 6,
      env: 'PRICE_GUESSING_ESTATE_HOUR_OF_DAILY_QUIZ',
    },
    minuteOfDailyQuiz: {
      doc: 'The minute when the daily guessing game is broadcasted with new estate selection.',
      format: Number,
      default: 0,
      env: 'PRICE_GUESSING_ESTATE_MINUTE_OF_DAILY_QUIZ',
    },
  },
  osloTransport: {
    departmentIdList: {
      doc: 'The department id list for Oslo Transport lead service range.',
      format: String,
      default: '',
      env: 'OSLO_TRANSPORT_DEPARTMENTID_LIST',
    },
  },
  lambda: {
    pdfGenFunctionName: {
      doc: 'The name of the pdf generator lambda function',
      format: String,
      default: '',
      env: 'LAMBDA_PDF_GENERATOR_NAME',
    },
  },
  hjemNo: {
    clientId: {
      doc: 'Hjem.no OAuth client ID',
      format: String,
      default: '',
      env: 'HJEM_NO_CLIENT_ID',
    },
    clientSecret: {
      doc: 'Hjem.no OAuth client secret',
      format: String,
      default: '',
      env: 'HJEM_NO_CLIENT_SECRET',
    },
    tokenUrl: {
      doc: 'Hjem.no OAuth token URL',
      format: String,
      default: 'https://apigw.hjem.no/admin-backend/api/v1/oauth/token',
      env: 'HJEM_NO_TOKEN_URL',
    },
    apiBaseUrl: {
      doc: 'Hjem.no API base URL',
      format: String,
      default: 'https://apigw.hjem.no/admin-backend/api/v1',
      env: 'HJEM_NO_API_BASE_URL',
    },
    systemName: {
      doc: 'System name for Hjem.no API (e.g., vitec_next)',
      format: String,
      default: 'vitec_next',
      env: 'HJEM_NO_SYSTEM_NAME',
    },
    installationId: {
      doc: 'Installation ID for Hjem.no API (e.g., MSNOP)',
      format: String,
      default: 'MSNOP',
      env: 'HJEM_NO_INSTALLATION_ID',
    },
  },
});

convictConfig.validate({ allowed: 'strict' });

export const config = convictConfig.getProperties();

export type ConfigObject = typeof config;

export const mongooseConnectionOptions: mongoose.ConnectOptions = {
  //useNewUrlParser: true,
  //useCreateIndex: true,
  //useFindAndModify: false,
  //useUnifiedTopology: true,
};

export enum FeatureFlag {
  BROKER_PASSWORD_CHANGE = 'broker-password-change',
  CUSTOMER_PASSWORD_CHANGE = 'customer-password-change',
  CUSTOMER_EMAIL_CHANGE = 'customer-email-change',
  CUSTUMER_DELETE_USER = 'customer-delete-user',
  BROKER_DELETE_USER = 'broker-delete-user',
  BYGGSTARY_LEAD = 'byggstart-lead',
  EXPONOVA_LEAD = 'exponova-lead',
  OVERTAKE_PROTOCOL = 'overtake-protocol-api',
  ESTATE_SYNC_BANKID = 'estate-sync-bankid',
  ESTATE_SYNC_VIPPS = 'estate-sync-vipps',
  SETTLEMENT_SELLER = 'settlement-seller',
  SETTLEMENT_BUYER = 'settlement-buyer',
  POLITICALLY_EXPOSED_PERSON_FORM = 'politically-exposed-person-form-api',
  DEDUPLICATE_ESTATES = 'deduplicate-estates',
  GET_BROKER_ESTATES_FOR_USER = 'get_broker_estates_for_user',
  LAMBDA_PDF_GENERATION = 'lambda-pdf-generation',
  USE_EIENDOMSVERDI_REST_API = 'use-eiendomsverdi-rest-api',
  ON_VITEC_SIGNABLE_DOCUMENTS = 'on-vitec-signable-documents',
  USE_VITEC_SMS_SENDING = 'use-vitec-sms-sending',
}
