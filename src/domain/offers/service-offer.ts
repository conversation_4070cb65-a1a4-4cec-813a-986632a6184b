export enum ServiceOfferViaEmailType {
  HMH_MOVING = 'HMH_MOVING',
  HMH_CLEANING = 'HMH_CLEANING',
  IF_INSURANCE = 'IF_INSURANCE',
  TRYGG = 'TRYGG',
  SSG = 'SSG',
  OSLO_TRANSPORT = 'OSLO_TRANSPORT',
  EXPO_NOVA = 'EXPO_NOVA',
  CHILL = 'CHILL',
  VERKET_INTERIOR = 'VERKET_INTERIOR',
  RENTAL = 'RENTAL',
}

// Not called through service-offer.service
export enum NotUnifiedServiceOfferViaEmailType {
  BYGGSTART = 'BYGGSTART',
  EXPONOVA = 'EXPONOVA',
}

export enum ServiceOfferViaApiType {
  STOREBRAND = 'STOREBRAND',
  STOREBRAND_VITEC = 'STOREBRAND_VITEC',
  SECTOR_ALARM = 'SECTOR_ALARM',
  TELENOR = 'TELENOR',
  STEDDY = 'STEDDY',
  KOKKELOREN = 'KOKKELOREN',
}

// Not called through service-offer.service
export enum NotUnifiedServiceOfferViaApiType {
  VERISURE = 'VERISURE',
  NORGES_ENERGI = 'NORGES_ENERGY',
  FORTUM = 'FORTUM',
  FJORDKRAFT = 'FJORDKRAFT',
  HKRAFT = 'HKRAFT',
}

export type AllServiceOfferTypes =
  | ServiceOfferViaEmailType
  | NotUnifiedServiceOfferViaEmailType
  | ServiceOfferViaApiType
  | NotUnifiedServiceOfferViaApiType;

export type ServiceOfferRequest = {
  address: string | null;
  brokerName: string | null;
  date: string | null;
  email: string;
  name: string;
  note: string | null;
  phone: string;
  postcode: string | null;
  type: ServiceOfferViaEmailType | ServiceOfferViaApiType;
};
