import type { OtpMeterResponse } from './otp-meter/otp-meter';
import type { OtpParticipantResponse } from './otp-participant/otp-participant';

export enum ElectricityProvider {
  FORTUM = 'fortum',
  HAFSLUND_FORTUM = 'hafslundFortum', // deprecated
  HAFSLUND = 'hafslund', // deprecated
  NORGES_ENERGY = 'norgesEnergy',
  FJORDKRAFT = 'fjordkraft',
  TRONDELAGSPOT = 'trondelagSpot',
  TRONDELAGTOBBSPOT = 'trondelagTobbSpot',
  HKRAFT = 'hkraft',
  NONE = 'none',
}

export enum NumberOfKeys {
  ONE = '1',
  TWO = '2',
  THREE = '3',
  FOUR = '4',
  FIVE_OR_MORE = '5+',
}

export type Otp = {
  id: string;
  estateVitecId: string;
  moneyTransferred: boolean | null;
  sellerNewAddress: string | null;
  sellerNewPostcode: string | null;
  sellerNewCity: string | null;
  propertyCleaned: boolean | null;
  propertyCleanedComment: string | null;
  sellerPaidCosts: boolean | null;
  sellerPaidCostsComment: string | null;
  handedOverAllKeys: boolean | null;
  handedOverAllKeysComment: string | null;
  numberOfKeys: NumberOfKeys | null;
  smokeAlarmAvailable: boolean | null;
  fireExtinguisherAvailable: boolean | null;
  fireSafetyComment: string | null;
  waterInfoProvided: boolean;
  electricityInfoProvided: boolean;
  signingStarted: Date | null;
  signingFinished: Date | null;
  fileId: string | null;
  idfyDocumentId: string | null;
  finalSettlement: boolean | null;
  finalSettlementComment: string | null;
  finalSettlementWithholding?: boolean | null;
  finalSettlementWithholdAmount?: string | null;
  finalSettlementWithholdReason?: string | null;
  finalSettlementWithholdingComment?: string | null;
  electricityProviderSelected: ElectricityProvider | null;
  handoverComment: string | null;
  // billing data
  address: string | null;
  postCode: string | null;
  city: string | null;
  billingBuyerContactId: string | null;
  locked: boolean;
};

export type OtpResponse = Otp & {
  electricityMeters: OtpMeterResponse[];
  waterMeters: OtpMeterResponse[];
  participants: OtpParticipantResponse[];
};

// https://hub.megler.vitec.net/Help/ResourceModel?modelName=EstateBaseType
export enum EstateBaseType {
  UNKNOWN = -1, // Ukjent
  NOT_SET = 0, // Ingen grunntype
  DETACHED = 1, // Bolig
  LEISURE = 2, //Fritid
  BUSINESS = 3, // Næring
  PROJECT = 4, // Prosjekt
  PLOT = 5, // Tomt
}

// https://hub.megler.vitec.net/Help/Api/GET-installationId-Next-EstateTypes it's user-pass and installationId protected though
export enum EstateTypeId {
  UKJENT = '0',
  ENEBOLIG = '1',
  TOMMANSBOLIG = '2',
  REKKEHUS = '3',
  SELVEIERLEILIGHET = '4',
  ANDELSLEILIGHET = '5',
  AKSJELEILIGHET = '6',
  FRITIDSEIENDOM = '7',
  TOMT = '8',
  KONTOR = '9',
  BYGARD = '10',
  KOMBINASJONSLOKALE = '11',
  GARASJE = '12',
  LAGER = '13',
  HOTELL = '14',
  INDUSTRI = '15',
  FORRETNING_BUTIKK = '16',
  LANDBRUK = '17',
  SMABRUK = '18',
  KJOPESENTER = '19',
  SERVERINGSLOKALE_KANTINE = '20',
  VERKSTED = '21',
  UNDERVISNING_ARRANGEMENT = '22',
  OBLIGASJONSLEILIGHET = '23',
  FIREMANNSBOLIG = '24',
  NAERINGSTOMT = '25',
  GARASJE_2 = '26',
}

export const isSigningResetRequest = (form: {
  signingStarted?: Date | null;
  idfyDocumentId?: string | null;
}): boolean => {
  return form.signingStarted === null && form.idfyDocumentId === null;
};

export const idfyDocumentIdAndSigningStartedProps: string[] = ['idfyDocumentId', 'signingStarted'];
