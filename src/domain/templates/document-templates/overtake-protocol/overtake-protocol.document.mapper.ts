import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import i18n from '../../../../i18n.config';
import { getEstateAddressString } from '../../../../utils/get-estate-address-string.util';
import type { EstateMongoose } from '../../../estate/mongo/estate.mongoose-types';
import type { LandIdentificationMatrix } from '../../../land-identification/LandIdentificationMatrix';
import type { OtpResponse } from '../../../otp/otp';
import { ElectricityProvider } from '../../../otp/otp';
import type { OtpMeterResponse } from '../../../otp/otp-meter/otp-meter';
import type { OtpParticipant } from '../../../otp/otp-participant/otp-participant';
import type { DashIfNull } from '../document-template.utils';
import { dashWrapper, emptyStringWrapper } from '../document-template.utils';

type Meter = { meterNumber: DashIfNull<string>; meterReading: DashIfNull<string>; meterName: DashIfNull<string> };
type Participant = {
  name: DashIfNull<string>;
  email: DashIfNull<string>;
  phoneNumber: DashIfNull<string>;
  type: string;
};
type OvertakeProtocolDocument = {
  estateAssignmentNumber: DashIfNull<string>;
  estateAddress: DashIfNull<string>;
  estateMunicipality: {
    id: string;
    name: string;
  };
  estateMatrikkel: LandIdentificationMatrix;
  takeOverDate: string;
  now: string;
  sellerNewAddress: DashIfNull<string>;
  electricityInfoProvided: boolean;
  waterInfoProvided: boolean;
  waterMeters: Meter[];
  electricityMeters: Meter[];
  moneyTransferred: boolean;
  propertyCleaned: boolean;
  handoverComment: DashIfNull<string>;
  propertyCleanedComment: DashIfNull<string>;
  sellerPaidCosts: boolean;
  sellerPaidCostsComment: DashIfNull<string>;
  handedOverAllKeys: boolean;
  handedOverAllKeysComment: DashIfNull<string>;
  numberOfKeys: DashIfNull<string>;
  smokeAlarmAvailable: boolean;
  fireExtinguisherAvailable: boolean;
  fireSafetyComment: DashIfNull<string>;
  finalSettlement: boolean;
  finalSettlementWithholding: boolean;
  finalSettlementWithholdAmount: DashIfNull<string>;
  finalSettlementWithholdingComment: DashIfNull<string>;
  finalSettlementComment: DashIfNull<string>;
  finalSettlementWithholdReason: DashIfNull<string>;
  sellers: Participant[];
  buyers: Participant[];
  electricityProviderSelected: DashIfNull<string>;
};

const ElectricityProviderString: { [key in ElectricityProvider]: string | null } = {
  [ElectricityProvider.FORTUM]: 'Fortum',
  [ElectricityProvider.HAFSLUND_FORTUM]: 'HafslundFortum', // deprecated
  [ElectricityProvider.HAFSLUND]: 'Hafslund', // deprecated
  [ElectricityProvider.NORGES_ENERGY]: 'Fortum (NorgesEnergi)',
  [ElectricityProvider.FJORDKRAFT]: 'Fjordkraft',
  [ElectricityProvider.TRONDELAGSPOT]: 'Trønderspot',
  [ElectricityProvider.TRONDELAGTOBBSPOT]: 'TOBB Spotpris',
  [ElectricityProvider.HKRAFT]: 'HKraft',
  [ElectricityProvider.NONE]: null,
};

type GetParticipants = (participants: OtpParticipant[]) => Participant[];
export const getParticipants: GetParticipants = (participants) =>
  participants.map((participant) => ({
    email: dashWrapper(participant.email),
    name: dashWrapper(participant.name),
    phoneNumber: dashWrapper(participant.phoneNumber),
    type: i18n.__(participant.belongsTo.charAt(0).toUpperCase() + participant.belongsTo.slice(1).toLowerCase()),
  }));

export const getBuyers: GetParticipants = (participants) =>
  getParticipants(participants.filter((participant) => participant.belongsTo === 'BUYER'));

export const getSellers: GetParticipants = (participants) =>
  getParticipants(participants.filter((participant) => participant.belongsTo === 'SELLER'));

type TransformMeters = (meters: OtpMeterResponse[]) => Meter[];
export const transformMeters: TransformMeters = (meters) =>
  meters.map((meter) => ({
    meterNumber: dashWrapper(meter.meterNumber),
    meterReading: dashWrapper(meter.meterReading),
    meterName: dashWrapper(meter.meterName),
  }));

export type OvertakeProtocolMapperInputData = {
  estate: Pick<
    EstateMongoose,
    'assignmentNum' | 'address' | 'municipality' | 'municipalityId' | 'matrikkel' | 'takeOverDate'
  >;
  otp: Pick<
    OtpResponse,
    | 'sellerNewAddress'
    | 'sellerNewPostcode'
    | 'sellerNewCity'
    | 'electricityInfoProvided'
    | 'waterInfoProvided'
    | 'waterMeters'
    | 'moneyTransferred'
    | 'propertyCleaned'
    | 'handoverComment'
    | 'propertyCleanedComment'
    | 'sellerPaidCosts'
    | 'sellerPaidCostsComment'
    | 'handedOverAllKeys'
    | 'handedOverAllKeysComment'
    | 'numberOfKeys'
    | 'smokeAlarmAvailable'
    | 'fireExtinguisherAvailable'
    | 'fireSafetyComment'
    | 'finalSettlement'
    | 'finalSettlementWithholding'
    | 'finalSettlementWithholdAmount'
    | 'finalSettlementWithholdingComment'
    | 'finalSettlementComment'
    | 'finalSettlementWithholdReason'
    | 'electricityMeters'
    | 'participants'
    | 'electricityProviderSelected'
  >;
  date: Date;
};
type OvertakeProtocolDocumentMapper = (input: OvertakeProtocolMapperInputData) => OvertakeProtocolDocument;
export const overtakeProtocolDocumentMapper: OvertakeProtocolDocumentMapper = ({ estate, otp, date }) => ({
  estateAssignmentNumber: dashWrapper(estate.assignmentNum),
  estateAddress: dashWrapper(getEstateAddressString(estate.address)),
  estateMunicipality: {
    id: emptyStringWrapper(estate.municipalityId),
    name: emptyStringWrapper(estate.municipality?.toLocaleUpperCase()),
  },
  estateMatrikkel: estate.matrikkel[0],
  takeOverDate: format(utcToZonedTime(estate.takeOverDate, 'Europe/Oslo'), 'dd.MM.yyyy'),
  now: format(utcToZonedTime(date, 'Europe/Oslo'), 'dd.MM.yyyy HH:mm:ss'),
  sellerNewAddress: dashWrapper(
    getEstateAddressString({
      city: emptyStringWrapper(otp.sellerNewCity),
      streetAdress: emptyStringWrapper(otp.sellerNewAddress),
      zipCode: emptyStringWrapper(otp.sellerNewPostcode),
    }),
  ),
  electricityInfoProvided: otp.electricityInfoProvided || false,
  waterInfoProvided: otp.waterInfoProvided || false,
  waterMeters: transformMeters(otp.waterMeters),
  electricityMeters: transformMeters(otp.electricityMeters),
  moneyTransferred: otp.moneyTransferred || false,
  propertyCleaned: otp.propertyCleaned || false,
  handoverComment: dashWrapper(otp.handoverComment),
  propertyCleanedComment: dashWrapper(otp.propertyCleanedComment),
  sellerPaidCosts: otp.sellerPaidCosts || false,
  sellerPaidCostsComment: dashWrapper(otp.sellerPaidCostsComment),
  handedOverAllKeys: otp.handedOverAllKeys || false,
  handedOverAllKeysComment: dashWrapper(otp.handedOverAllKeysComment),
  numberOfKeys: dashWrapper(otp.numberOfKeys),
  smokeAlarmAvailable: otp.smokeAlarmAvailable || false,
  fireExtinguisherAvailable: otp.fireExtinguisherAvailable || false,
  fireSafetyComment: dashWrapper(otp.fireSafetyComment),
  finalSettlement: otp.finalSettlement || false,
  finalSettlementWithholding: otp.finalSettlementWithholding || false,
  finalSettlementWithholdAmount: dashWrapper(otp.finalSettlementWithholdAmount || null),
  finalSettlementWithholdingComment: dashWrapper(otp.finalSettlementWithholdingComment ?? null),
  finalSettlementComment: dashWrapper(otp.finalSettlementComment || null),
  finalSettlementWithholdReason: dashWrapper(otp.finalSettlementWithholdReason || null),
  electricityProviderSelected: dashWrapper(
    ElectricityProviderString[otp.electricityProviderSelected || ElectricityProvider.NONE],
  ),
  sellers: getSellers(otp.participants),
  buyers: getBuyers(otp.participants),
});
