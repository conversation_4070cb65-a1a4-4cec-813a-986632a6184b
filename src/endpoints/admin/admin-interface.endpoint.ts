import type { Endpoint } from '../../framework/endpoint/endpoint';
import { EndpointMethod } from '../../framework/endpoint/endpoint';
import type { Request, Response } from '../../framework/endpoint/request-response';

type AdminInterfaceRequest = Request<
  Record<string, never>,
  Record<string, never>,
  Record<string, never>,
  Record<string, never>
>;
type AdminInterfaceResponse = Response<string, Record<string, never>>;
type AdminInterfaceEndpoint = Endpoint<AdminInterfaceRequest, AdminInterfaceResponse>;

type AdminInterfaceEndpointFactory = () => AdminInterfaceEndpoint;

const adminInterfaceHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nordvik Admin - User Impersonation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .auth-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .search-section {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: calc(100% - 20px);
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .user-list {
            margin-top: 20px;
        }
        .user-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            background: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-info {
            flex-grow: 1;
        }
        .user-name {
            font-weight: 500;
            color: #333;
        }
        .user-details {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .impersonate-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 12px;
        }
        .impersonate-btn:hover {
            background: #1e7e34;
        }
        .token-result {
            margin-top: 20px;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
        }
        .token-text {
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .hidden {
            display: none;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Nordvik Admin - User Impersonation</h1>
        
        <div class="auth-section">
            <h3>Authentication</h3>
            <div class="form-group">
                <label for="apiKey">Admin API Key:</label>
                <input type="password" id="apiKey" placeholder="Enter your admin API key">
            </div>
            <button onclick="authenticate()">Authenticate</button>
            <div id="authStatus"></div>
        </div>

        <div id="mainInterface" class="hidden">
            <div class="search-section">
                <h3>Search Users</h3>
                <div class="form-group">
                    <label for="searchQuery">Search by email, name, or phone:</label>
                    <input type="text" id="searchQuery" placeholder="Enter search term (minimum 2 characters)">
                </div>
                <button onclick="searchUsers()">Search</button>
                <div id="searchResults" class="user-list"></div>
            </div>

            <div id="tokenResult" class="hidden">
                <h3>Impersonation Token</h3>
                <p>Use this token in the Authorization header as: <code>Bearer [token]</code></p>
                <div class="token-text" id="tokenText"></div>
                <button onclick="copyToken()">Copy Token</button>
            </div>
        </div>
    </div>

    <script>
        let currentApiKey = '';
        let currentToken = '';

        async function authenticate() {
            const apiKey = document.getElementById('apiKey').value;
            const statusDiv = document.getElementById('authStatus');
            
            if (!apiKey) {
                showStatus(statusDiv, 'Please enter an API key', 'error');
                return;
            }

            try {
                const response = await fetch('/admin/auth', {
                    method: 'POST',
                    headers: {
                        'x-api-key': apiKey
                    }
                });

                if (response.ok) {
                    currentApiKey = apiKey;
                    showStatus(statusDiv, 'Authentication successful!', 'success');
                    document.getElementById('mainInterface').classList.remove('hidden');
                } else {
                    showStatus(statusDiv, 'Authentication failed. Please check your API key.', 'error');
                }
            } catch (error) {
                showStatus(statusDiv, 'Network error. Please try again.', 'error');
            }
        }

        async function searchUsers() {
            const query = document.getElementById('searchQuery').value;
            const resultsDiv = document.getElementById('searchResults');
            
            if (!query || query.length < 2) {
                resultsDiv.innerHTML = '<div class="error">Please enter at least 2 characters</div>';
                return;
            }

            try {
                const response = await fetch(\`/admin/users/search?query=\${encodeURIComponent(query)}&limit=10\`, {
                    headers: {
                        'x-api-key': currentApiKey
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayUsers(data.users);
                } else {
                    resultsDiv.innerHTML = '<div class="error">Search failed. Please try again.</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = '<div class="error">Network error. Please try again.</div>';
            }
        }

        function displayUsers(users) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (users.length === 0) {
                resultsDiv.innerHTML = '<div class="error">No users found</div>';
                return;
            }

            const userItems = users.map(user => \`
                <div class="user-item">
                    <div class="user-info">
                        <div class="user-name">\${user.name}</div>
                        <div class="user-details">
                            Email: \${user.email}<br>
                            Phone: \${user.phoneNumber}<br>
                            ID: \${user.id}
                        </div>
                    </div>
                    <button class="impersonate-btn" onclick="impersonateUser('\${user.id}')">
                        Impersonate
                    </button>
                </div>
            \`).join('');

            resultsDiv.innerHTML = userItems;
        }

        async function impersonateUser(userId) {
            try {
                const response = await fetch('/admin/users/impersonate', {
                    method: 'POST',
                    headers: {
                        'x-api-key': currentApiKey,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ userId })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.token;
                    document.getElementById('tokenText').textContent = data.token;
                    document.getElementById('tokenResult').classList.remove('hidden');
                } else {
                    alert('Failed to generate impersonation token');
                }
            } catch (error) {
                alert('Network error. Please try again.');
            }
        }

        function copyToken() {
            navigator.clipboard.writeText(currentToken).then(() => {
                alert('Token copied to clipboard!');
            });
        }

        function showStatus(element, message, type) {
            element.innerHTML = \`<div class="status \${type}">\${message}</div>\`;
        }

        // Allow Enter key to trigger search
        document.getElementById('searchQuery').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });

        // Allow Enter key to trigger authentication
        document.getElementById('apiKey').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                authenticate();
            }
        });
    </script>
</body>
</html>
`;

export const adminInterfaceEndpointFactory: AdminInterfaceEndpointFactory = () => ({
  method: EndpointMethod.GET,
  route: '/admin',
  schema: {
    summary: 'Admin interface for user impersonation',
    description: 'Web interface for product owners to impersonate users',
    tags: ['Admin'],
    response: {
      200: {
        type: 'string',
        description: 'HTML page for admin interface',
      },
    },
  },
  handler: async () => {
    return {
      status: 200,
      response: adminInterfaceHTML,
      type: 'text/html',
      headers: {},
    };
  },
});
