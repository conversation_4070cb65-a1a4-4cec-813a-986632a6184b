import type { RateLimitConfig } from '../../config';
import { baseOAErrorResponses } from '../../domain/+oa_components/responses.oa-component';
import type { Endpoint, EndpointHandler, EndpointRequest, EndpointResponse } from '../../framework/endpoint/endpoint';
import { EndpointMethod } from '../../framework/endpoint/endpoint';
import type { Request, Response } from '../../framework/endpoint/request-response';
import type { WithAuth } from '../../framework/endpoint/wrappers/auth-endpoint-wrapper';
import type { ApikeyAuthenticatedUseCase } from '../../use-cases/apikey-authenticated.use-case';
import type { CreateElectricityLeadUseCase } from '../../use-cases/leads/create-electricity-lead.use-case';

type ReqBody = { estateId: string };
type Query = null;
type ReqParams = null;
// eslint-disable-next-line @typescript-eslint/naming-convention
type ReqHeaders = { 'x-api-key': string };
type ResBody = Record<string, never>;
type ResHeaders = null;

type Req = Request<ReqBody, Query, ReqParams, ReqHeaders>;
type Res = Response<ResBody, ResHeaders>;

type CreateElectricityLeadEndpointHandler = WithAuth<EndpointHandler<Req, Res>>;
type CreateElectricityLeadEndpoint = Endpoint<
  EndpointRequest<CreateElectricityLeadEndpointHandler>,
  EndpointResponse<CreateElectricityLeadEndpointHandler>
>;

type CreateElectricityLeadEndpointFactory = (params: {
  createElectricityLeadUseCase: ApikeyAuthenticatedUseCase<CreateElectricityLeadUseCase>;
  rateLimit: RateLimitConfig;
}) => CreateElectricityLeadEndpoint;

export const createElectricityLeadEndpointFactory: CreateElectricityLeadEndpointFactory = ({
  createElectricityLeadUseCase,
  rateLimit,
}) => ({
  method: EndpointMethod.POST,
  route: '/lead/electricity',
  schema: {
    summary: 'Resend leads for OTP form',
    tags: ['Leads'],
    headers: {
      type: 'object',
      required: ['x-api-key'],
      properties: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'x-api-key': { type: 'string' },
      },
    },
    body: {
      type: 'object',
      required: ['estateId'],
      properties: {
        estateId: {
          type: 'string',
        },
      },
    },
    response: {
      [201]: { type: 'object' },
      [403]: baseOAErrorResponses[403],
      [500]: baseOAErrorResponses[500],
    },
  },
  handler: async (request) => {
    await createElectricityLeadUseCase({
      apiKey: request.headers['x-api-key'],
      estateId: request.body.estateId,
    });
    return {
      headers: null,
      status: 201,
      response: {},
    };
  },
  rateLimit: {
    max: rateLimit.max,
    timeWindow: rateLimit.timeWindow,
  },
});
