import type { RateLimitConfig } from '../../config';
import { baseOAErrorResponses } from '../../domain/+oa_components/responses.oa-component';
import type { Endpoint, EndpointHandler, EndpointRequest, EndpointResponse } from '../../framework/endpoint/endpoint';
import { EndpointMethod } from '../../framework/endpoint/endpoint';
import type { Request, Response } from '../../framework/endpoint/request-response';
import type { WithAuth } from '../../framework/endpoint/wrappers/auth-endpoint-wrapper';
import type { ApikeyAuthenticatedUseCase } from '../../use-cases/apikey-authenticated.use-case';
import type { CreateSettlementBuyerLeadUseCase } from '../../use-cases/leads/create-settlement-buyer-lead.use-case';

type ReqBody = { estateId: string; estateBaseType?: number };
type Query = null;
type ReqParams = null;
// eslint-disable-next-line @typescript-eslint/naming-convention
type ReqHeaders = { 'x-api-key': string };
type ResBody = Record<string, never>;
type ResHeaders = null;

type Req = Request<ReqBody, Query, ReqParams, ReqHeaders>;
type Res = Response<ResBody, ResHeaders>;

type CreateSettlementBuyerLeadEndpointHandler = WithAuth<EndpointHandler<Req, Res>>;
type CreateSettlementBuyerLeadEndpoint = Endpoint<
  EndpointRequest<CreateSettlementBuyerLeadEndpointHandler>,
  EndpointResponse<CreateSettlementBuyerLeadEndpointHandler>
>;

type CreateSettlementBuyerLeadEndpointFactory = (params: {
  createSettlementBuyerLeadUseCase: ApikeyAuthenticatedUseCase<CreateSettlementBuyerLeadUseCase>;
  rateLimit: RateLimitConfig;
}) => CreateSettlementBuyerLeadEndpoint;

export const createSettlementBuyerLeadEndpointFactory: CreateSettlementBuyerLeadEndpointFactory = ({
  createSettlementBuyerLeadUseCase,
  rateLimit,
}) => ({
  method: EndpointMethod.POST,
  route: '/lead/settlement-buyer',
  schema: {
    summary: 'Resend leads for settlement buyer form',
    tags: ['Leads'],
    headers: {
      type: 'object',
      required: ['x-api-key'],
      properties: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'x-api-key': { type: 'string' },
      },
    },
    body: {
      type: 'object',
      required: ['estateId'],
      properties: {
        estateId: {
          type: 'string',
        },
        estateBaseType: {
          type: 'number',
          description: 'Optional estate base type for project settlements',
        },
      },
    },
    response: {
      [201]: { type: 'object' },
      [403]: baseOAErrorResponses[403],
      [404]: baseOAErrorResponses[404],
      [500]: baseOAErrorResponses[500],
    },
  },
  handler: async (request) => {
    await createSettlementBuyerLeadUseCase({
      apiKey: request.headers['x-api-key'],
      estateId: request.body.estateId,
      estateBaseType: request.body.estateBaseType,
    });
    return {
      headers: null,
      status: 201,
      response: {},
    };
  },
  rateLimit: {
    max: rateLimit.max,
    timeWindow: rateLimit.timeWindow,
  },
});
