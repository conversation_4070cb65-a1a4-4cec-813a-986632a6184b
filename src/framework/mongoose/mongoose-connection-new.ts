import mongoose from 'mongoose';
import { config, mongooseConnectionOptions } from '../../config';
import type { Logger } from '../../logger';

export const mongooseConnectionFactory = ({
  connectionString = config.mongo.connection,
  db = config.mongo.db,
}: {
  connectionString: string;
  db: string;
  logger: Logger;
}): mongoose.Connection => {
  return mongoose.createConnection(connectionString, mongooseConnectionOptions).useDb(db);
};

// New connection factory for nordvik-api compatible database
export const mongooseNewConnectionFactory = ({
  uri = config.mongoNew.uri,
  logger,
}: {
  uri?: string;
  logger: Logger;
}): mongoose.Connection => {
  logger.info(`Connecting to new MongoDB database: ${uri}`);
  return mongoose.createConnection(uri, {
    ...mongooseConnectionOptions,
    autoIndex: false,
    connectTimeoutMS: 30000,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 360000,
    bufferCommands: false,
  });
};
