import type { QueryInterface } from 'sequelize';
import { ElectricityProvider } from '../domain/otp/otp';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(
        `ALTER TYPE "enum_OvertakeProtocol_electricityProviderSelected" ADD VALUE IF NOT EXISTS '${ElectricityProvider.HKRAFT}'`,
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
  down: async (_queryInterface: QueryInterface) => {
    return;
  },
};
