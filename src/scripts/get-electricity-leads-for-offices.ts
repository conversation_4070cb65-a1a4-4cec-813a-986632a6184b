import { add } from 'date-fns';
import ExcelJS from 'exceljs';
import type { DepartmentMongoose } from '../domain/department/department';
import { departmentMongooseRepositoryFactory } from '../domain/department/department.mongoose-repository';
import { estateMongooseRepositoryFactory } from '../domain/estate/mongo/estate.mogoose-repository';
import type { BrokerIdWithRole, EstateMongoose } from '../domain/estate/mongo/estate.mongoose-types';
import { BrokerRole } from '../domain/estate/mongo/estate.mongoose-types';
import type { ExternalLeadAudit } from '../domain/external-lead-audit/external-lead-audit';
import { externalLeadAuditModelInit } from '../domain/external-lead-audit/external-lead-audit.model';
import { ExternalLeadAuditRepositoryFactory } from '../domain/external-lead-audit/external-lead-audit.repository';
import { NotUnifiedServiceOfferViaApiType } from '../domain/offers/service-offer';
import type { Otp } from '../domain/otp/otp';
import { ElectricityProvider } from '../domain/otp/otp';
import { overtakeProtocolParticipantModelInit } from '../domain/otp/otp-participant/otp-participant.model';
import { overtakeProtocolModelInit, setOvertakeProtocolModelReferences } from '../domain/otp/otp.model';
import { overtakeProtocolRepositoryFactory } from '../domain/otp/otp.repository';
import { mongooseNewConnectionFactory } from '../framework/mongoose/mongoose-connection';
import { sequelizeConnection } from '../framework/sequelize/connection';
import { logger } from '../logger';

const YEAR_FROM = 2023;
const MONTH_FROM = '12';
const DAY_FROM = '20';
const YEAR_TO = 2023;
const MONTH_TO = '12';
const DAY_TO_INCLUDED = '31';
const FILE_NAME = `output-files/Electricity-leads-including-none-${YEAR_FROM}_${MONTH_FROM}_${DAY_FROM}-${YEAR_TO}_${MONTH_TO}_${DAY_TO_INCLUDED}.xlsx`;

// DO NOT FORGET TO SET LOCAL .ENV FILE TO POINT TO PROD POSTGRES AND MONGO

// Using NEW MongoDB connection for testing migration
const mongooseConnection = mongooseNewConnectionFactory({
  logger,
});
const sequelize = sequelizeConnection(false);
overtakeProtocolModelInit({ sequelize });
overtakeProtocolParticipantModelInit({ sequelize });
setOvertakeProtocolModelReferences();
externalLeadAuditModelInit({ sequelize });

const estateMongooseRepo = estateMongooseRepositoryFactory({ connection: mongooseConnection });
const departmentMongooseRepo = departmentMongooseRepositoryFactory({ connection: mongooseConnection });

const otpRepository = overtakeProtocolRepositoryFactory();
const externalLeadRepo = ExternalLeadAuditRepositoryFactory();

type RowInfo = Pick<Otp, 'estateVitecId' | 'electricityProviderSelected' | 'signingFinished'> &
  Partial<Pick<EstateMongoose, 'takeOverDate' | 'departmentId'>> &
  Partial<{
    departmentName: DepartmentMongoose['name'];
  }> &
  Partial<EstateMongoose['address']> &
  Partial<BrokerIdWithRole> &
  Partial<BrokerIdWithRole['employee']> & {
    electricityLeadType: ExternalLeadAudit['leadType'] | 'No electricity lead found';
    wasLeadSentSuccessfully: boolean;
  };

const getData = async (): Promise<RowInfo[]> => {
  console.log('Getting OTP-s...');
  const otpsInRange = await otpRepository.getAllBetweenDates({
    yearFrom: YEAR_FROM,
    monthFrom: MONTH_FROM,
    dayFrom: DAY_FROM,
    yearTo: YEAR_TO,
    monthTo: MONTH_TO,
    dayToIncluded: DAY_TO_INCLUDED,
  });
  console.log('Getting estates details...');
  const vitecEstatesTakeOverInRange = await estateMongooseRepo.getAllEstates({
    takeOverYearFrom: YEAR_FROM,
    takeOverMonthFrom: MONTH_FROM,
    takeOverYearTo: YEAR_TO + 1, // it does not hurt to get more estates, we iterate through OTPs
  });
  console.log('Getting departments details...');
  const allDepartments = await departmentMongooseRepo.getDepartments();
  console.log('Getting External Lead Audit records...');
  const leadsSent = await externalLeadRepo.getInTimeRange({
    fromDate: new Date(`${YEAR_FROM}-${MONTH_FROM}-${DAY_FROM}`),
    toDate: add(new Date(`${YEAR_TO}-${MONTH_TO}-${DAY_TO_INCLUDED}`), { days: 1 }),
  });
  return otpsInRange.map((otp) => {
    const estateForOtp = vitecEstatesTakeOverInRange.find((ve) => ve.estateId === otp.estateVitecId);
    const deparmentOfOtp = allDepartments.find((d) => d.departmentId === estateForOtp?.departmentId);
    const leadForOtp = leadsSent.find(
      (l) =>
        [
          NotUnifiedServiceOfferViaApiType.FORTUM,
          NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
          NotUnifiedServiceOfferViaApiType.NORGES_ENERGI,
          'HAFSLUND',
          'HAFSLUND_FORTUM',
        ].includes(l.leadType) && (l.data?.notes as { otpId: string | undefined })?.otpId === otp.id,
    );
    return {
      estateVitecId: otp.estateVitecId,
      electricityProviderSelected: otp.electricityProviderSelected || ElectricityProvider.NONE,
      electricityLeadType: leadForOtp?.leadType || 'No electricity lead found',
      wasLeadSentSuccessfully: leadForOtp?.isSuccessful || false,
      signingFinished: otp.signingFinished,
      takeOverDate: estateForOtp?.takeOverDate,
      zipCode: estateForOtp?.address.zipCode,
      city: estateForOtp?.address.city,
      streetAddress: estateForOtp?.address.streetAdress,
      mainBrokerId: estateForOtp?.brokersIdWithRoles.find((b) => b.brokerRole === BrokerRole.MAIN_BROKER)?.employeeId,
      mainBrokerName: estateForOtp?.brokersIdWithRoles.find((b) => b.brokerRole === BrokerRole.MAIN_BROKER)?.employee
        .name,
      departmentId: deparmentOfOtp?.departmentId,
      departmentName: deparmentOfOtp?.name,
    };
  });
};

const writeToExcelFile = async (rows: RowInfo[]): Promise<void> => {
  console.log('Creating excel file...');
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('example', { views: [{ state: 'frozen', xSplit: 0, ySplit: 1 }] });
  sheet.columns = [
    {
      header: 'estateVitecId',
      key: 'estateVitecId',
      width: 40,
    },
    { header: 'electricityProviderSelected', key: 'electricityProviderSelected', width: 20 },
    { header: 'electricityLeadType', key: 'electricityLeadType', width: 20 },
    { header: 'wasLeadSentSuccessfully', key: 'wasLeadSentSuccessfully', width: 20 },
    { header: 'signingFinished', key: 'signingFinished', width: 15 },
    { header: 'takeOverDate', key: 'takeOverDate', width: 15 },
    { header: 'zipCode', key: 'zipCode', width: 10 },
    { header: 'city', key: 'city', width: 15 },
    { header: 'streetAddress', key: 'streetAddress', width: 30 },
    { header: 'mainBrokerId', key: 'mainBrokerId', width: 10 },
    { header: 'mainBrokerName', key: 'mainBrokerName', width: 20 },
    { header: 'departmentId', key: 'departmentId', width: 10 },
    { header: 'departmentName', key: 'departmentName', width: 20 },
  ];
  sheet.addRows(rows);
  sheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFFF00' },
    bgColor: { argb: 'FF0000FF' },
  };
  await workbook.xlsx.writeFile(FILE_NAME);
};

void getData()
  .then(async (data) => {
    await sequelize.close();
    void writeToExcelFile(data).then(() => {
      console.log('Process done, check the generated file');
      process.exit(0);
    });
  })
  .catch(async (e) => {
    console.error(e);
    await sequelize.close();
  });
