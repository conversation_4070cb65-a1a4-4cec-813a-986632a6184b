import type { EstateService } from '../../domain/estate/estate.service';
import { ServiceOfferViaApiType } from '../../domain/offers/service-offer';
import type { SettlementSellerRepository } from '../../domain/settlement-seller/settlement-seller.repository';
import { SettlementLeadViaEmailProvider } from '../../domain/settlement/settlement-leads';
import type { SettlementService } from '../../domain/settlement/settlement.service';
import { BadRequest } from '../../framework/errors/bad-request.error';
import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { AsyncUseCase } from '../../framework/use-case/async.use-case';
import { logger } from '../../logger';
import type { WithApiKey } from '../apikey-authenticated.use-case';

export type Input = WithApiKey<{
  estateId: string;
}>;

export type CreateSettlementSellerLeadUseCase = AsyncUseCase<Input, void>;

export const createSettlementSellerLeadUseCaseFactory = ({
  settlementSellerRepository,
  settlementService,
  estateService,
}: {
  settlementSellerRepository: SettlementSellerRepository;
  settlementService: SettlementService;
  estateService: EstateService;
}): CreateSettlementSellerLeadUseCase => async ({ estateId }: Input): Promise<void> => {
  logger.info(`Settlement seller lead sending started for estate: ${estateId}`);

  // Get the settlement seller form
  const settlementSeller = await settlementSellerRepository.get({ estateId });

  if (!settlementSeller) {
    throw new ResourceNotFound('Settlement seller form not found');
  }

  if (!settlementSeller.leads) {
    throw new BadRequest('Settlement seller form has no leads to resend');
  }

  // Get estate information
  const estate = await estateService.getEstateFromVitec(estateId);
  if (!estate) {
    throw new ResourceNotFound('Estate not found');
  }

  logger.info(`Settlement seller lead sending in progress for estate: ${estateId}. Settlement form and estate found.`);

  // Send all email leads
  const emailLeadProviders = [
    SettlementLeadViaEmailProvider.IF,
    SettlementLeadViaEmailProvider.HMH_CLEANING,
    SettlementLeadViaEmailProvider.HMH_MOVING,
    SettlementLeadViaEmailProvider.OSLO_TRANSPORT,
    SettlementLeadViaEmailProvider.EXPO_NOVA,
    SettlementLeadViaEmailProvider.CHILL,
    SettlementLeadViaEmailProvider.VERKET_INTERIOR,
    SettlementLeadViaEmailProvider.RENTAL,
  ];

  const emailLeadPromises = emailLeadProviders.map(async (provider) =>
    settlementService.sendEmailLead({
      estate,
      settlementLeads: settlementSeller.leads,
      settlementParticipants: settlementSeller.participants,
      settlementLeadProvider: provider,
      signedInUserId: null,
      settlementBuyerOrSellerId: settlementSeller.id,
    }),
  );

  // Send all API leads
  const apiLeadProviders = [
    ServiceOfferViaApiType.TELENOR,
    ServiceOfferViaApiType.STEDDY,
    ServiceOfferViaApiType.KOKKELOREN,
  ];

  const apiLeadPromises = apiLeadProviders.map(async (provider) =>
    settlementService.sendApiLead({
      estate,
      settlementLeads: settlementSeller.leads,
      settlementParticipants: settlementSeller.participants,
      settlementLeadProvider: provider,
      signedInUserId: null,
      settlementBuyerOrSellerId: settlementSeller.id,
    }),
  );

  // Execute all lead sending operations
  const results = await Promise.allSettled([...emailLeadPromises, ...apiLeadPromises]);

  // Log any warnings or errors
  results.forEach((result, index) => {
    if (result.status === 'fulfilled' && result.value.warning) {
      logger.warn(`Settlement seller lead sending warning for estate ${estateId}: ${result.value.warning}`);
    } else if (result.status === 'rejected') {
      logger.error(
        result.reason as Error,
        `Settlement seller lead sending failed for estate ${estateId} at index ${index}`,
      );
    }
  });

  logger.info(`Settlement seller lead sending completed for estate: ${estateId}`);
};
